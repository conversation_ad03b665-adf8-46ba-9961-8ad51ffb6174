<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>这是一个案例</title>
    <style>
      * {
        margin: 0px;
        padding: 0px;
      }
    </style>
    <!-- 具体路径配置，你根据自己文件目录设置，我的是课件中源码形式 -->
    <script type="importmap">
      {
        "imports": {
          "three": "../lib/three.module.js",
          "three/examples/jsm/controls/OrbitControls": "../lib/examples/jsm/controls/OrbitControls.js",
          "three/examples/jsm/loaders/TextureLoader": "../lib/examples/jsm/loaders/TextureLoader.js",
          "three/addons/libs/lil-gui.module.min.js": "../lib/examples/jsm/libs/lil-gui.module.min.js",
          "three/addons/libs/stats.module.js": "../lib/examples/jsm/libs/stats.module.js",
          "three/addons/math/ImprovedNoise.js": "../lib/examples/jsm/math/ImprovedNoise.js",
          "three/addons/postprocessing/EffectComposer.js": "../lib/examples/jsm/postprocessing/EffectComposer.js",
          "three/addons/postprocessing/RenderPass.js": "../lib/examples/jsm/postprocessing/RenderPass.js",
          "three/addons/postprocessing/UnrealBloomPass.js": "../lib/examples/jsm/postprocessing/UnrealBloomPass.js",
          "three/addons/postprocessing/ShaderPass.js": "../lib/examples/jsm/postprocessing/ShaderPass.js"
        }
      }
    </script>
  </head>
  <body>
    <div class="container"></div>
  </body>
  <script type="module">
    import * as THREE from "three";
    import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
    import { GUI } from "three/addons/libs/lil-gui.module.min.js";
    import no1, {
      setUniformsConfig,
      getParticleSystem,
      createCloud,
      area,
    } from "./no1/index.js";
    import Stats from "three/addons/libs/stats.module.js";
    import { createNebula } from "./no1/test.js";

    const gui = new GUI();
    const stats = new Stats();
    window.document.body.appendChild(stats.dom);
    const props = {
      uTimeStep: 0,
    };
    // gui.add(props, "", 1, 100, 0.1);

    const container = document.querySelector(".container");

    // 初始化场景
    const scene = new THREE.Scene({
      background: new THREE.Color("blue"),
    });
    scene.fog = new THREE.Fog(0x000000, 0, 100);
    // 相机
    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    camera.position.set(10, 15, 10);

    // 渲染器
    const renderer = new THREE.WebGLRenderer({
      alias: true,
    });
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setSize(window.innerWidth, window.innerHeight);
    container.appendChild(renderer.domElement);
    //轨道控制器
    const controls = new OrbitControls(camera, renderer.domElement);

    // 坐标xzy辅助线
    const axesHelper = new THREE.AxesHelper(10);
    // axesHelper.position.set(0, -15, 0);
    scene.add(axesHelper);
    // camera.position.z = 5;e3

    window.onresize = () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
      orderControls.update();
    };

    // async function getGlsl(
    //   vetFileName = "vert.glsl",
    //   fragFilename = "frag.glsl"
    // ) {
    //   const base = "./";
    //   function _readFile(path) {
    //     return new Promise(async (resolve, reject) => {
    //       const res = await fetch(base + path);
    //       const code = await res.text();
    //       resolve(code);
    //     });
    //   }

    //   const vert = await _readFile(vetFileName);
    //   const frag = await _readFile(fragFilename);
    //   return { vert, frag };
    // }

    async function init() {
      const no1Mesh = await no1(THREE);
      const particle = await getParticleSystem(THREE);
      const testMesh = createNebula();
      // scene.add(no1Mesh);
      // scene.add(particle);
      // scene.add(createCloud(THREE));
      // scene.add(testMesh);
      const area1 = await area(THREE);
      scene.add(area1);
      // const area2 = await area(THREE);
      // area2.position.set(0, 0, -10);
      // scene.add(area2);
      // const area3 = await area(THREE);
      // area3.position.set(0, 0, 10);
      // scene.add(area3);
      // const area4 = await area(THREE);
      // area4.position.set(-10, 0, 0);
      // scene.add(area4);
      // const area5 = await area(THREE);
      // area5.position.set(10, 0, 0);
      // scene.add(area5);
      // const area6 = await area(THREE);
      // area6.position.set(0, 10, 0);
      // scene.add(area6);
      console.log(scene);
      animation();

      const parameters = {
        threshold: 0.25,
        opacity: 0.25,
        range: 0.1,
        steps: 100,
      };
      function update() {
        testMesh.material.uniforms.threshold.value = parameters.threshold;
        testMesh.material.uniforms.opacity.value = parameters.opacity;
        testMesh.material.uniforms.range.value = parameters.range;
        testMesh.material.uniforms.steps.value = parameters.steps;
      }

      gui.add(parameters, "threshold", 0, 1, 0.01).onChange(update);
      gui.add(parameters, "opacity", 0, 1, 0.01).onChange(update);
      gui.add(parameters, "range", 0, 1, 0.01).onChange(update);
      gui.add(parameters, "steps", 0, 200, 1).onChange(update);
      // 监听gui事件改变

      function animation() {
        requestAnimationFrame(animation);
        // no1Mesh.material.uniforms.iTime.value += 0.01;
        // if (no1Mesh.material.uniforms.iTime.value > 130)
        //   no1Mesh.material.uniforms.iTime.value = 10;
        // particle.rotateY(0.001);
        renderer.render(scene, camera);
        stats.update();
        // testMesh.material.uniforms.time.value += 0.01;

        // no1Mesh.material.uniforms.uTime.value += props.uTimeStep;
      }
    }
    init();
  </script>
</html>
