<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>这是一个案例</title>
    <style>
      * {
        margin: 0px;
        padding: 0px;
      }
    </style>
    <!-- 具体路径配置，你根据自己文件目录设置，我的是课件中源码形式 -->
    <script type="importmap">
      {
        "imports": {
          "three": "../lib/three.module.js",
          "three/examples/jsm/controls/OrbitControls": "../lib/examples/jsm/controls/OrbitControls.js",
          "three/examples/jsm/loaders/TextureLoader": "../lib/examples/jsm/loaders/TextureLoader.js",
          "three/addons/libs/lil-gui.module.min.js": "../lib/examples/jsm/libs/lil-gui.module.min.js"
        }
      }
    </script>
  </head>
  <body>
    <div class="container"></div>
  </body>
  <script type="module">
    import * as THREE from "three";
    import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
    import { GUI } from "three/addons/libs/lil-gui.module.min.js";
    import no1, { setUniformsConfig } from "./no1/index.js";
    const gui = new GUI();
    const props = {
      uTimeStep: 0,
    };
    gui.add(props, "uTimeStep", 0.001, 0.015, 0.000001);

    const container = document.querySelector(".container");

    // 初始化场景
    const scene = new THREE.Scene();

    // 相机
    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    camera.position.set(20, 20, 20);

    // 渲染器
    const renderer = new THREE.WebGLRenderer({
      alias: true,
      background: true,
    });
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setSize(window.innerWidth, window.innerHeight);
    container.appendChild(renderer.domElement);
    //轨道控制器
    const controls = new OrbitControls(camera, renderer.domElement);

    // 坐标xzy辅助线
    const axesHelper = new THREE.AxesHelper(10);
    axesHelper.position.set(0, -15, 0);
    scene.add(axesHelper);
    // camera.position.z = 5;

    window.onresize = () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
      orderControls.update();
    };

    // async function getGlsl(
    //   vetFileName = "vert.glsl",
    //   fragFilename = "frag.glsl"
    // ) {
    //   const base = "./";
    //   function _readFile(path) {
    //     return new Promise(async (resolve, reject) => {
    //       const res = await fetch(base + path);
    //       const code = await res.text();
    //       resolve(code);
    //     });
    //   }

    //   const vert = await _readFile(vetFileName);
    //   const frag = await _readFile(fragFilename);
    //   return { vert, frag };
    // }

    async function init() {
      const no1Mesh = await no1(THREE);
      scene.add(no1Mesh);
      console.log(scene);
      animation();
      gui
        .add(no1Mesh.material.uniforms.uA, "value", 0.0, 1.0, 0.001)
        .name("uA")
        .onChange((v) => {
          setUniformsConfig(no1Mesh.material.uniforms);
        });
      gui
        .add(no1Mesh.material.uniforms.uB, "value", 0.0, 2.0, 0.001)
        .name("uB")
        .onChange((v) => {
          setUniformsConfig(no1Mesh.material.uniforms);
        });
      gui
        .add(no1Mesh.material.uniforms.uR, "value", 0.0, 1.0, 0.001)
        .name("uR")
        .onChange((v) => {
          setUniformsConfig(no1Mesh.material.uniforms);
        });
      gui
        .add(no1Mesh.material.uniforms.uH, "value", 0.001, 0.05, 0.001)
        .name("uH")
        .onChange((v) => {
          setUniformsConfig(no1Mesh.material.uniforms);
        });
      // 监听gui事件改变

      function animation() {
        requestAnimationFrame(animation);
        renderer.render(scene, camera);
        // no1Mesh.material.uniforms.uTime.value += props.uTimeStep;
      }
    }
    init();
  </script>
</html>
