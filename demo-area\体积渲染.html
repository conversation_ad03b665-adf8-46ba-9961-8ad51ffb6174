<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Volumetric Fog in Three.js</title>
    <style>
      body {
        margin: 0;
        overflow: hidden;
      }
      canvas {
        display: block;
      }
    </style>
  </head>
  <body>
    <script type="importmap">
      {
        "imports": {
          "three": "../lib/three.module.js",
          "three/examples/jsm/controls/OrbitControls": "../lib/examples/jsm/controls/OrbitControls.js",
          "three/examples/jsm/loaders/TextureLoader": "../lib/examples/jsm/loaders/TextureLoader.js",
          "three/addons/libs/lil-gui.module.min.js": "../lib/examples/jsm/libs/lil-gui.module.min.js",
          "three/addons/libs/stats.module.js": "../lib/examples/jsm/libs/stats.module.js",
          "three/addons/math/ImprovedNoise.js": "../lib/examples/jsm/math/ImprovedNoise.js"
        }
      }
    </script>
    <script type="text" id="vertex">
      varying vec3 vWorldPosition;

      void main() {
        vec4 worldPosition = modelMatrix * vec4(position, 1.0);
        vWorldPosition = worldPosition.xyz;
        gl_Position = projectionMatrix * viewMatrix * worldPosition;
      }
    </script>
    <script type="text" id="fragment">
       //精度
      precision highp float;
      precision highp sampler3D;

      uniform sampler3D uTexture;
      uniform vec3 uCameraPos;
      varying vec3 vWorldPosition;
      //星云shader相关传参
      varying vec2 vUv;
      uniform float iTime;
      uniform vec2 iResolution;
      #define SCREEN_EFFECT 0

      const int STEPS = 100;
      const float DENSITY_THRESHOLD = 0.01;
      const float STEP_SIZE = 1.0/ float(STEPS);
      //-------星云实现shader-------

        // random/hash function
        float hash( float n )
        {
          return fract(cos(n)*41415.92653);
        }

        // 2d noise function
        float noise( in vec2 x )
        {
          vec2 p  = floor(x);
          vec2 f  = smoothstep(0.0, 1.0, fract(x));
          float n = p.x + p.y*57.0;

          return mix(mix( hash(n+  0.0), hash(n+  1.0),f.x),
            mix( hash(n+ 57.0), hash(n+ 58.0),f.x),f.y);
        }

        float noise( in vec3 x )
        {
          vec3 p  = floor(x);
          vec3 f  = smoothstep(0.0, 1.0, fract(x));
          float n = p.x + p.y*57.0 + 113.0*p.z;

          return mix(mix(mix( hash(n+  0.0), hash(n+  1.0),f.x),
            mix( hash(n+ 57.0), hash(n+ 58.0),f.x),f.y),
            mix(mix( hash(n+113.0), hash(n+114.0),f.x),
            mix( hash(n+170.0), hash(n+171.0),f.x),f.y),f.z);
        }

        mat3 m = mat3( 0.00,  1.60,  1.20, -1.60,  0.72, -0.96, -1.20, -0.96,  1.28 );

        // Fractional Brownian motion
        float fbmslow( vec3 p )
        {
          float f = 0.5000*noise( p ); p = m*p*1.2;
          f += 0.2500*noise( p ); p = m*p*1.3;
          f += 0.1666*noise( p ); p = m*p*1.4;
          f += 0.0834*noise( p ); p = m*p*1.84;
          return f;
        }

        float fbm( vec3 p )
        {
          float f = 0., a = 1., s=0.;
          f += a*noise( p ); p = m*p*1.149; s += a; a *= .75;
          f += a*noise( p ); p = m*p*1.41; s += a; a *= .75;
          f += a*noise( p ); p = m*p*1.51; s += a; a *= .65;
          f += a*noise( p ); p = m*p*1.21; s += a; a *= .35;
          f += a*noise( p ); p = m*p*1.41; s += a; a *= .75;
          f += a*noise( p );
          return f/s;
        }



        vec4 mainImage(vec2 fragCoord, float alpha_in)
        {
        	float time = iTime * 0.1;

        	// vec2 xy = -1.0 + 2.0*fragCoord.xy / iResolution.xy;
        	vec2 xy = fragCoord * 20.0;

        	// fade in (1=10sec), out after 8=80sec;
        	float fade = min(1., time*1.)*min(1.,max(0., 15.-time));
        	// start glow after 5=50sec
        	float fade2= max(0., time-10.)*0.37;
        	float glow = max(-.25,1.+pow(fade2, 10.) - 0.001*pow(fade2, 25.));


        	// get camera position and view direction
        	// vec3 campos = vec3(500.0, 850., -.0-cos((time-1.4)/2.)*2000.); // moving
        	vec3 campos =  vec3(0.0, 1500.0, 0.0);
        	vec3 camtar = vec3(0., 0., 0.);

        	float roll = 0.34;
        	vec3 cw = normalize(camtar-campos);
        	vec3 cp = vec3(sin(roll), cos(roll),0.0);
        	vec3 cu = normalize(cross(cw,cp));
        	vec3 cv = normalize(cross(cu,cw));
        	vec3 rd = normalize( xy.x*cu + xy.y*cv + 1.6*cw );

        	vec3 light   = normalize( vec3(  0., 0.,  0. )-campos );
        	float sundot = clamp(dot(light,rd),0.0,1.0);

        	// render sky

            // galaxy center glow
            vec3 col = glow*1.2*min(vec3(1.0, 1.0, 1.0), vec3(2.0,1.0,0.5)*pow( sundot, 100.0 ));
            // moon haze
            col += 0.3*vec3(0.8,0.9,1.2)*pow( sundot, 8.0 );

        	// stars
        	// vec3 stars = 85.5*vec3(pow(fbmslow(rd.xyz*312.0), 7.0))*vec3(pow(fbmslow(rd.zxy*440.3), 8.0));

        	// moving background fog
            // vec3 cpos = 1500.*rd + vec3(831.0-time*30., 321.0, 1000.0);
          //   col += vec3(0.4, 0.5, 1.0) * ((fbmslow( cpos*0.0035 ) - .5));

        	// cpos += vec3(831.0-time*33., 321.0, 999.);
          //   col += vec3(0.6, 0.3, 0.6) * 10.0*pow((fbmslow( cpos*0.0045 )), 10.0);

        	// cpos += vec3(3831.0-time*39., 221.0, 999.0);
          //   col += 0.03*vec3(0.6, 0.0, 0.0) * 10.0*pow((fbmslow( cpos*0.0145 )), 2.0);

        	// stars
        	// cpos = 1500.*rd + vec3(831.0, 321.0, 999.);
        	// col += stars*fbm(cpos*0.0021);


        	// Clouds
            vec2 shift = vec2( time*100.0, time*180.0 );
            vec4 sum = vec4(0,0,0,0);
            float c = campos.y / rd.y; // cloud height
            vec3 cpos2 = campos - c*rd;
            float radius = length(cpos2.xz)/1000.0;

            if (radius<1.8)
            {
          	  for (int q=10; q>-10; q--) // layers
              {
        		if (sum.w>0.999) continue;
                float c = (float(q)*8.-campos.y) / rd.y; // cloud height
                vec3 cpos = campos + c*rd;

           		float see = dot(normalize(cpos), normalize(campos));
        		vec3 lightUnvis = vec3(.0,.0,.0 );
        		vec3 lightVis   = vec3(1.3,1.2,1.2 );
        		vec3 shine = mix(lightVis, lightUnvis, smoothstep(0.0, 1.0, see));

        		// border
         	    float radius = length(cpos.xz)/999.;
        	    if (radius>1.0)
        	      continue;

        		float rot = 3.00*(radius)-time;
              	cpos.xz = cpos.xz*mat2(cos(rot), -sin(rot), sin(rot), cos(rot));

        		cpos += vec3(831.0+shift.x, 321.0+float(q)*mix(250.0, 50.0, radius)-shift.x*0.2, 1330.0+shift.y); // cloud position
        		cpos *= mix(0.0025, 0.0028, radius); // zoom
              	float alpha = smoothstep(0.50, 1.0, fbm( cpos )); // fractal cloud density
        	  	alpha *= 1.3*pow(smoothstep(1.0, 0.0, radius), 0.3); // fade out disc at edges
        	  	// vec3 dustcolor = mix(vec3( 2.0, 1.3, 1.0 ), vec3( 0.1,0.2,0.3 ), pow(radius, .5));======>edit
        		vec3 centerColor = vec3(2.0, 1.3, 1.0);       // 原中心颜色
        		vec3 edgeColor = vec3(0.25, 0.4, 0.8);         // 蓝紫色边缘

        		float edgeMix = pow(radius, 0.7);             // 控制颜色渐变的半径感知强度
        		vec3 dustcolor = mix(centerColor, edgeColor, edgeMix); //===================>
              	vec3 localcolor = mix(dustcolor, shine, alpha); // density color white->gray

        		float gstar = 2.*pow(noise( cpos*21.40 ), 22.0);
        		float gstar2= 3.*pow(noise( cpos*26.55 ), 34.0);
        		float gholes= 1.*pow(noise( cpos*11.55 ), 14.0);
        		localcolor += vec3(1.0, 0.6, 0.3)*gstar;
        		localcolor += vec3(1.0, 1.0, 0.7)*gstar2;
        		localcolor -= gholes;

                alpha = (1.0-sum.w)*alpha; // alpha/density saturation (the more a cloud layer\\\'s density, the more the higher layers will be hidden)
                sum += vec4(localcolor*alpha, alpha); // sum up weightened color
        	  }

          	  for (int q=0; q<20; q++) // 120 layers
              {
        		if (sum.w>0.999) continue;
                float c = (float(q)*4.-campos.y) / rd.y; // cloud height
                vec3 cpos = campos + c*rd;

           		float see = dot(normalize(cpos), normalize(campos));
        		vec3 lightUnvis = vec3(.0,.0,.0 );
        		vec3 lightVis   = vec3(1.3,1.2,1.2 );
        		vec3 shine = mix(lightVis, lightUnvis, smoothstep(0.0, 1.0, see));

        		// border
         	    float radius = length(cpos.xz)/200.0;
        	    if (radius>1.0)
        	      continue;

        		float rot = 3.2*(radius)-time*1.1;
              	cpos.xz = cpos.xz*mat2(cos(rot), -sin(rot), sin(rot), cos(rot));

        		cpos += vec3(831.0+shift.x, 321.0+float(q)*mix(250.0, 50.0, radius)-shift.x*0.2, 1330.0+shift.y); // cloud position
              	float alpha = 0.1+smoothstep(0.6, 1.0, fbm( cpos )); // fractal cloud density
        	  	alpha *= 1.2*(pow(smoothstep(1.0, 0.0, radius), 0.72) - pow(smoothstep(1.0, 0.0, radius*1.875), 0.2)); // fade out disc at edges
              	vec3 localcolor = vec3(0.0, 0.0, 0.0); // density color white->gray

                alpha = (1.0-sum.w)*alpha; // alpha/density saturation (the more a cloud layer\\\'s density, the more the higher layers will be hidden)
                sum += vec4(localcolor*alpha, alpha); // sum up weightened color
        	  }
            }
        	float alpha = smoothstep(1.-radius*.5, 1.0, sum.w);
            sum.rgb /= sum.w+0.0001;
            sum.rgb -= 0.2*vec3(0.8, 0.75, 0.7) * pow(sundot,10.0)*alpha;
            sum.rgb += min(glow, 10.0)*0.2*vec3(1.2, 1.2, 1.2) * pow(sundot,5.0)*(1.0-alpha);

           	col = mix( col, sum.rgb , sum.w);//*pow(sundot,10.0);

            // haze
        	col = fade*mix(col, vec3(0.3,0.5,.9), 29.0*(pow( sundot, 50.0 )-pow( sundot, 60.0 ))/(2.+9.*abs(rd.y)));

        #if SCREEN_EFFECT == 1
            if (time<2.5)
            {
            	// screen effect
            	float c = (col.r+col.g+col.b)* .3 * (.6+.3*cos(gl_FragCoord.y*1.2543)) + .1*(noise((xy+time*2.)*294.)*noise((xy-time*3.)*321.));
           	    c += max(0.,.08*sin(10.*time+xy.y*7.2543));
                // flicker
        		col = vec3(c, c, c) * (1.-0.5*pow(noise(vec2(time*99., 0.)), 9.));
            }
            else
            {
                // bam
                float c = clamp(1.-(time-2.5)*6., 0., 1. );
                col = mix(col, vec3(1.,1.,1.),c);
            }
        #endif

            // Vignetting
        	vec2 xy2 = gl_FragCoord.xy / iResolution.xy;
        	col *= vec3(.5, .5, .5) + 0.25*pow(100.0*xy2.x*xy2.y*(1.0-xy2.x)*(1.0-xy2.y), .5 );
        	return vec4(col,alpha_in);
        }
      //--------end--------------

      //运行主函数
      void main() {
        vec3 rayDir = normalize(vWorldPosition - uCameraPos);
        vec3 boxMin = vec3(-0.5);
        vec3 boxMax = vec3(0.5);
        vec3 rayOrigin = uCameraPos;

        // Intersect ray with box
        vec3 invDir = 1.0 / rayDir;
        vec3 tMin = (boxMin - rayOrigin) * invDir;
        vec3 tMax = (boxMax - rayOrigin) * invDir;
        vec3 t1 = min(tMin, tMax);
        vec3 t2 = max(tMin, tMax);
        float tNear = max(max(t1.x, t1.y), t1.z);
        float tFar = min(min(t2.x, t2.y), t2.z);
        if (tNear > tFar || tFar < 0.0) discard;

        float t = max(tNear, 0.0);
        vec3 pos;
        vec4 sum = vec4(0.0,0.0,0.0,0.0);
        for (int i = 0; i < STEPS; i++) {
          if (t > tFar || sum.a > 0.95) break;
          pos = rayOrigin + rayDir * t;
          vec3 texCoord = pos + 0.5; // 转化为0-1之间的坐标

          vec4 sample2 = texture(uTexture, texCoord);

          //--二维平面的星云

          float alpha = sample2.a; // 控制透明度影响
          vec3 col = sample2.rgb * 5.0;
          sum.rgb += col * (1.0 - sum.a);
          sum.a += (1.0 - sum.a) * alpha * 5.0;
          t += STEP_SIZE;
        }

        gl_FragColor = sum;
      }
    </script>
    <script type="module">
      import * as THREE from "three";
      import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
      import { ImprovedNoise } from "three/addons/math/ImprovedNoise.js";

      const vertexShader = document.getElementById("vertex").textContent;
      const fragmentShader = document.getElementById("fragment").textContent;
      // console.log(vertexShader);

      // Create scene
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(
        45,
        window.innerWidth / window.innerHeight,
        0.1,
        100
      );
      camera.position.set(0, 0, 2);

      // Renderer
      const renderer = new THREE.WebGLRenderer();
      renderer.setSize(window.innerWidth, window.innerHeight);
      document.body.appendChild(renderer.domElement);

      // Controls
      const controls = new OrbitControls(camera, renderer.domElement);

      function randomColor() {
        return new THREE.Color(Math.random() * 0xffffff);
      }

      // Create 3D Texture
      // const size = 128;
      // const data = new Uint8Array(size * size * size * 4);
      // const perlin = new ImprovedNoise();
      // let i = 0;

      // const center = size / 2;
      // const scale = 0.05;

      // for (let z = 0; z < size; z++) {
      //   for (let y = 0; y < size; y++) {
      //     for (let x = 0; x < size; x++) {
      //       const nx = x - center;
      //       const ny = y - center;
      //       const nz = z - center;

      //       // 计算极坐标（用于螺旋臂）
      //       const r = Math.sqrt(nx * nx + ny * ny);
      //       const theta = Math.atan2(ny, nx); // 角度
      //       const phi = Math.atan2(nz, r); // 竖直方向角度

      //       // 螺旋臂结构（偏移角度，创建螺旋臂）
      //       const spiralFactor = 2.0;
      //       const spiralTheta = theta - r * 0.1 * spiralFactor;

      //       // 加入多层噪声
      //       const n1 = perlin.noise(r * scale, spiralTheta * 2.0, phi * 2.0);
      //       const n2 = perlin.noise(
      //         x * scale * 0.5,
      //         y * scale * 0.5,
      //         z * scale * 0.5
      //       );
      //       const density = Math.max(0, n1 * 0.8 + n2 * 0.2 - 0.3);

      //       // 颜色渐变（根据角度变化）
      //       const hue = (spiralTheta + Math.PI) / (2 * Math.PI); // 转换为 0~1
      //       // const color = new THREE.Color().setHSL(hue, 1.0, 0.6); // 彩色渐变
      //       const color = new THREE.Color(1, 0, 0);

      //       // 衰减中心强度（模拟星云边缘）
      //       const falloff =
      //         1.0 -
      //         Math.min(1, Math.sqrt(nx * nx + ny * ny + nz * nz) / center);

      //       const alpha = density * falloff;

      //       data[i++] = color.r * 255 * alpha;
      //       data[i++] = color.g * 255 * alpha;
      //       data[i++] = color.b * 255 * alpha;
      //       data[i++] = alpha * 255;
      //     }
      //   }
      // }

      // 创建体积纹理
      const width = 64, //宽
        height = 64, //高
        depth = 64; //深度
      const size = width * height * depth; //定义体积纹理体素大小
      const data = new Uint8Array(size * 4); // RGBA 每个体素4个分量

      const color1 = new THREE.Color(0x5500ff); // 深紫
      const color2 = new THREE.Color(0x00ccff); // 青蓝
      const color3 = new THREE.Color(0xff00cc); // 粉红

      for (let z = 0; z < depth; z++) {
        for (let y = 0; y < height; y++) {
          for (let x = 0; x < width; x++) {
            const i = x + y * width + z * width * height;
            const stride = i * 4; // 计算当前体素在data中的索引

            // 中心坐标
            const cx = width / 2;
            const cy = height / 2;
            const cz = depth / 2;

            // 距离计算（x-y平面上为圆形）
            const dx = (x - cx) / (width / 2);
            const dy = (y - cy) / (height / 2);
            const dz = (z - cz) / (depth / 2);

            const r2 = dx * dx + dy * dy; // 当前半径平方
            const zFactor = 0.5 - Math.abs(dz); //z轴到集合中心的距离, 中心处厚，边缘薄

            // 控制衰减（radius falloff + z衰减）
            const density = Math.max(0.0, 1.0 - r2) * zFactor * 0.2;

            const noise = Math.random() * 0.5 + 0.75;

            data[stride] = 255; // R
            data[stride + 1] = 0; // G
            data[stride + 2] = 0; // B

            // 初始化透明度为0
            let alpha = 0;
            // 体积云圆盘的厚度范围
            const range = 0.5;

            //在范围中的z轴透明度为1,不在透明度为0
            if (z >= depth / 2 - range && z < depth / 2 + range) {
              alpha = Math.floor(density * 255);
            }
            // 写入透明度
            data[stride + 3] = alpha; // Alpha
          }
        }
      }

      const texture = new THREE.Data3DTexture(data, width, height, depth);
      texture.format = THREE.RGBAFormat; // RGBA格式纹理
      texture.type = THREE.UnsignedByteType;
      texture.minFilter = THREE.LinearFilter;
      texture.magFilter = THREE.LinearFilter;
      texture.unpackAlignment = 1;
      texture.needsUpdate = true; // 更新纹理

      // Shader Material
      const material = new THREE.ShaderMaterial({
        vertexShader,
        fragmentShader,
        transparent: true,
        uniforms: {
          uTexture: { value: texture },
          uCameraPos: { value: camera.position },
          iResolution: {
            value: new THREE.Vector2(window.innerWidth, window.innerHeight),
          },
          iTime: {
            value: 1.0,
          },
        },
        side: THREE.BackSide,
      });

      const geometry = new THREE.BoxGeometry(1, 1, 1);
      const mesh = new THREE.Mesh(geometry, material);
      scene.add(mesh);

      // Resize
      window.addEventListener("resize", () => {
        renderer.setSize(window.innerWidth, window.innerHeight);
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
      });

      // Animate
      renderer.setAnimationLoop(() => {
        controls.update();
        material.uniforms.uCameraPos.value.copy(camera.position);
        renderer.render(scene, camera);
      });
    </script>
  </body>
</html>
