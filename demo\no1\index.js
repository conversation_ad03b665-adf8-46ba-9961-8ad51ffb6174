export default async function no1(THREE) {
  const { vertexShader, fragmentShader } = await getGlsl();
  // 创建一个球体几何体
  const geometry = new THREE.BufferGeometry();
  geometry.setFromPoints([new THREE.Vector3(0, 0, 0)]);
  const position = new Float32Array([0, 0, 0, 10, 0, 0, 0, 10, 0, 10, 11, 10]);
  geometry.setAttribute("position", new THREE.BufferAttribute(position, 3));
  const material = new THREE.ShaderMaterial({
    vertexShader,
    fragmentShader,
    transparent: true,
    uniforms: {
      uA: {
        value: 0.15,
      },
      uB: {
        value: 0.0,
      },
      uR: {
        value: 0.015,
      },
      uH: {
        value: 0.0,
      },
      // ulength: {
      //   value: 1.0,
      // },
      // iResolution: {
      //   value: new THREE.Vector2(window.innerWidth, window.innerHeight),
      // },
    },
    // 混合模式
    blending: THREE.AdditiveBlending,
    // depthTest: false,
  });
  // 初始化
  Object.assign(material.uniforms, getUniformsConfig());
  const mesh = new THREE.Points(geometry, material);
  return mesh;
}

async function getGlsl(vetFileName = "vert.glsl", fragFilename = "frag.glsl") {
  const base = "./no1/";
  function _readFile(path) {
    return new Promise(async (resolve, reject) => {
      const res = await fetch(base + path);
      const code = await res.text();
      resolve(code);
    });
  }

  const vertexShader = await _readFile(vetFileName);
  const fragmentShader = await _readFile(fragFilename);
  return { vertexShader, fragmentShader };
}

function getUniformsConfig() {
  const store = localStorage.getItem("uniforms-config");
  return store ? JSON.parse(store) : {};
}

export function setUniformsConfig(config) {
  localStorage.setItem("uniforms-config", JSON.stringify(config));
}
