<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Volumetric Fog in Three.js</title>
    <style>
      body {
        margin: 0;
        overflow: hidden;
      }
      canvas {
        display: block;
      }
    </style>
  </head>
  <body>
    <script type="importmap">
      {
        "imports": {
          "three": "../lib/three.module.js",
          "three/examples/jsm/controls/OrbitControls": "../lib/examples/jsm/controls/OrbitControls.js",
          "three/examples/jsm/loaders/TextureLoader": "../lib/examples/jsm/loaders/TextureLoader.js",
          "three/addons/libs/lil-gui.module.min.js": "../lib/examples/jsm/libs/lil-gui.module.min.js",
          "three/addons/libs/stats.module.js": "../lib/examples/jsm/libs/stats.module.js",
          "three/addons/math/ImprovedNoise.js": "../lib/examples/jsm/math/ImprovedNoise.js"
        }
      }
    </script>
    <!-- 星云体积着色器 -->
    <script type="text" id="vertex">
      varying vec3 vWorldPosition;
      varying vec3 vPosition;

      void main() {
        vec4 worldPosition = modelMatrix * vec4(position, 1.0);
        vWorldPosition = worldPosition.xyz;
        vPosition = position;
        gl_Position = projectionMatrix * viewMatrix * worldPosition;
      }
    </script>

    <script type="text" id="fragment">
      uniform float time;
      uniform vec3 cameraPosition;
      uniform sampler3D volumeTexture;
      uniform vec3 nebulaColor1;
      uniform vec3 nebulaColor2;
      uniform vec3 nebulaColor3;
      uniform vec3 nebulaColor4;
      uniform float density;
      uniform float brightness;
      uniform float turbulence;
      uniform float absorption;

      varying vec3 vWorldPosition;
      varying vec3 vPosition;

      // 3D噪声函数
      vec3 mod289(vec3 x) {
        return x - floor(x * (1.0 / 289.0)) * 289.0;
      }

      vec4 mod289(vec4 x) {
        return x - floor(x * (1.0 / 289.0)) * 289.0;
      }

      vec4 permute(vec4 x) {
        return mod289(((x*34.0)+1.0)*x);
      }

      vec4 taylorInvSqrt(vec4 r) {
        return 1.79284291400159 - 0.85373472095314 * r;
      }

      float snoise(vec3 v) {
        const vec2 C = vec2(1.0/6.0, 1.0/3.0);
        const vec4 D = vec4(0.0, 0.5, 1.0, 2.0);

        vec3 i = floor(v + dot(v, C.yyy));
        vec3 x0 = v - i + dot(i, C.xxx);

        vec3 g = step(x0.yzx, x0.xyz);
        vec3 l = 1.0 - g;
        vec3 i1 = min(g.xyz, l.zxy);
        vec3 i2 = max(g.xyz, l.zxy);

        vec3 x1 = x0 - i1 + C.xxx;
        vec3 x2 = x0 - i2 + C.yyy;
        vec3 x3 = x0 - D.yyy;

        i = mod289(i);
        vec4 p = permute(permute(permute(
                   i.z + vec4(0.0, i1.z, i2.z, 1.0))
                 + i.y + vec4(0.0, i1.y, i2.y, 1.0))
                 + i.x + vec4(0.0, i1.x, i2.x, 1.0));

        float n_ = 0.142857142857;
        vec3 ns = n_ * D.wyz - D.xzx;

        vec4 j = p - 49.0 * floor(p * ns.z * ns.z);

        vec4 x_ = floor(j * ns.z);
        vec4 y_ = floor(j - 7.0 * x_);

        vec4 x = x_ *ns.x + ns.yyyy;
        vec4 y = y_ *ns.x + ns.yyyy;
        vec4 h = 1.0 - abs(x) - abs(y);

        vec4 b0 = vec4(x.xy, y.xy);
        vec4 b1 = vec4(x.zw, y.zw);

        vec4 s0 = floor(b0)*2.0 + 1.0;
        vec4 s1 = floor(b1)*2.0 + 1.0;
        vec4 sh = -step(h, vec4(0.0));

        vec4 a0 = b0.xzyw + s0.xzyw*sh.xxyy;
        vec4 a1 = b1.xzyw + s1.xzyw*sh.zzww;

        vec3 p0 = vec3(a0.xy, h.x);
        vec3 p1 = vec3(a0.zw, h.y);
        vec3 p2 = vec3(a1.xy, h.z);
        vec3 p3 = vec3(a1.zw, h.w);

        vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2, p2), dot(p3,p3)));
        p0 *= norm.x;
        p1 *= norm.y;
        p2 *= norm.z;
        p3 *= norm.w;

        vec4 m = max(0.6 - vec4(dot(x0,x0), dot(x1,x1), dot(x2,x2), dot(x3,x3)), 0.0);
        m = m * m;
        return 42.0 * dot(m*m, vec4(dot(p0,x0), dot(p1,x1), dot(p2,x2), dot(p3,x3)));
      }

      // 分层噪声函数 - 增强版
      float fbm(vec3 p) {
        float value = 0.0;
        float amplitude = 0.5;
        float frequency = 1.0;

        for(int i = 0; i < 8; i++) {
          value += amplitude * snoise(p * frequency);
          amplitude *= 0.5;
          frequency *= 2.0;
        }
        return value;
      }

      // 湍流噪声
      float turbulentNoise(vec3 p) {
        float value = 0.0;
        float amplitude = 1.0;
        float frequency = 1.0;

        for(int i = 0; i < 6; i++) {
          value += amplitude * abs(snoise(p * frequency));
          amplitude *= 0.5;
          frequency *= 2.0;
        }
        return value;
      }

      // 脊状噪声
      float ridgedNoise(vec3 p) {
        float value = 0.0;
        float amplitude = 1.0;
        float frequency = 1.0;

        for(int i = 0; i < 5; i++) {
          float n = snoise(p * frequency);
          n = 1.0 - abs(n);
          n = n * n;
          value += amplitude * n;
          amplitude *= 0.5;
          frequency *= 2.0;
        }
        return value;
      }

      void main() {
        vec3 rayDir = normalize(vWorldPosition - cameraPosition);
        vec3 rayStart = vPosition * 0.5 + 0.5; // 转换到0-1范围

        // 体积渲染参数
        float stepSize = 0.008;
        int maxSteps = 120;

        vec4 color = vec4(0.0);
        vec3 currentPos = rayStart;
        float totalDistance = 0.0;

        for(int i = 0; i < maxSteps; i++) {
          if(color.a >= 0.98) break;

          // 多层动态噪声采样
          vec3 samplePos = currentPos + time * 0.015;
          vec3 turbulentPos = currentPos + time * 0.03;

          // 基础星云形状
          float baseNoise = fbm(samplePos * 1.5);
          float detailNoise = fbm(samplePos * 6.0 + vec3(50.0));
          float turbulent = turbulentNoise(turbulentPos * 3.0 + vec3(100.0)) * turbulence;
          float ridged = ridgedNoise(samplePos * 2.5 + vec3(200.0));

          // 组合噪声创建复杂的星云密度
          float nebulaDensity = baseNoise * 0.5 + detailNoise * 0.3 + turbulent * 0.15 + ridged * 0.05;
          nebulaDensity = smoothstep(0.2, 0.9, nebulaDensity) * density;

          // 距离衰减
          float distanceFromCenter = length(currentPos - vec3(0.5));
          float falloff = 1.0 - smoothstep(0.3, 0.7, distanceFromCenter);
          nebulaDensity *= falloff;

          if(nebulaDensity > 0.005) {
            // 复杂的颜色混合
            float colorNoise1 = fbm(samplePos * 3.0 + vec3(300.0));
            float colorNoise2 = fbm(samplePos * 5.0 + vec3(400.0));
            float colorNoise3 = turbulentNoise(samplePos * 4.0 + vec3(500.0));

            // 多层颜色混合
            vec3 nebulaColor = mix(nebulaColor1, nebulaColor2, colorNoise1 * 0.5 + 0.5);
            nebulaColor = mix(nebulaColor, nebulaColor3, colorNoise2 * 0.4 + 0.3);
            nebulaColor = mix(nebulaColor, nebulaColor4, colorNoise3 * 0.3 + 0.2);

            // 增强发光效果
            float glow = 1.0 - distanceFromCenter * 1.5;
            glow = max(0.0, glow);
            glow = pow(glow, 2.0);

            // 内部发光
            float innerGlow = 1.0 - smoothstep(0.0, 0.4, distanceFromCenter);
            innerGlow = pow(innerGlow, 3.0);

            // 边缘发光
            float edgeGlow = smoothstep(0.3, 0.6, distanceFromCenter) * (1.0 - smoothstep(0.6, 0.8, distanceFromCenter));

            float totalGlow = glow + innerGlow * 2.0 + edgeGlow * 1.5;

            // 应用亮度和发光
            vec3 finalColor = nebulaColor * brightness * (1.0 + totalGlow);

            // 光吸收效果
            float absorptionFactor = exp(-totalDistance * absorption);
            finalColor *= absorptionFactor;

            vec4 sampleColor = vec4(finalColor, nebulaDensity);

            // 改进的Alpha混合
            float alpha = sampleColor.a * (1.0 - color.a);
            color.rgb += sampleColor.rgb * alpha;
            color.a += alpha;
          }

          currentPos += rayDir * stepSize;
          totalDistance += stepSize;

          // 边界检查
          if(any(lessThan(currentPos, vec3(0.0))) || any(greaterThan(currentPos, vec3(1.0)))) {
            break;
          }
        }

        // 后处理增强
        color.rgb = pow(color.rgb, vec3(0.9)); // 轻微的伽马校正
        color.rgb *= 1.2; // 整体亮度提升

        gl_FragColor = color;
      }
    </script>

    <!-- 星尘粒子着色器 -->
    <script type="text" id="stardustVertex">
      attribute float size;
      attribute vec3 color;
      attribute float brightness;
      attribute float twinkle;

      uniform float time;

      varying vec3 vColor;
      varying float vSize;
      varying float vBrightness;
      varying float vTwinkle;

      void main() {
        vColor = color;
        vSize = size;
        vBrightness = brightness;
        vTwinkle = twinkle;

        vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);

        // 动态大小变化（闪烁效果）
        float twinkleEffect = 1.0 + sin(time * twinkle + position.x * 10.0) * 0.3;
        gl_PointSize = size * twinkleEffect * (400.0 / -mvPosition.z);

        gl_Position = projectionMatrix * mvPosition;
      }
    </script>

    <script type="text" id="stardustFragment">
      uniform float time;

      varying vec3 vColor;
      varying float vSize;
      varying float vBrightness;
      varying float vTwinkle;

      void main() {
        vec2 center = gl_PointCoord - vec2(0.5);
        float dist = length(center);

        if(dist > 0.5) discard;

        // 创建星形效果
        float angle = atan(center.y, center.x);
        float spikes = abs(sin(angle * 4.0)) * 0.3 + 0.7;

        // 径向渐变
        float radial = 1.0 - smoothstep(0.0, 0.5 * spikes, dist);

        // 闪烁效果
        float twinkleEffect = 1.0 + sin(time * vTwinkle) * 0.4;

        // 核心发光
        float core = 1.0 - smoothstep(0.0, 0.2, dist);
        core = pow(core, 2.0);

        // 外围光晕
        float halo = 1.0 - smoothstep(0.2, 0.5, dist);
        halo = pow(halo, 0.5);

        float alpha = (core * 2.0 + halo * 0.8) * radial * twinkleEffect * vBrightness;
        alpha = clamp(alpha, 0.0, 1.0);

        // 颜色增强
        vec3 finalColor = vColor * (1.0 + core * 2.0);

        gl_FragColor = vec4(finalColor, alpha);
      }
    </script>

    <script type="module">
      import * as THREE from "three";
      import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
      import { ImprovedNoise } from "three/addons/math/ImprovedNoise.js";
      import GUI from "three/addons/libs/lil-gui.module.min.js";
      import Stats from "three/addons/libs/stats.module.js";

      // 全局变量
      let scene, camera, renderer, controls, stats;
      let nebulaMesh, stardustSystem;
      let volumeTexture;
      let clock = new THREE.Clock();

      // GUI参数
      const params = {
        nebulaColor1: "#4a90e2", // 蓝色
        nebulaColor2: "#8e44ad", // 紫色
        nebulaColor3: "#e91e63", // 粉色
        nebulaColor4: "#ff9800", // 橙色
        density: 0.8,
        brightness: 1.2,
        turbulence: 0.6,
        absorption: 0.3,
        stardustCount: 15000,
        stardustSize: 2.0,
        animationSpeed: 1.0,
      };

      // 初始化场景
      function init() {
        // 创建场景
        scene = new THREE.Scene();
        scene.background = new THREE.Color(0x000011);

        // 创建相机
        camera = new THREE.PerspectiveCamera(
          75,
          window.innerWidth / window.innerHeight,
          0.1,
          1000
        );
        camera.position.set(0, 0, 3);

        // 创建渲染器
        renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(window.devicePixelRatio);
        document.body.appendChild(renderer.domElement);

        // 轨道控制器
        controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;
        controls.minDistance = 1;
        controls.maxDistance = 10;

        // 性能监控
        stats = new Stats();
        document.body.appendChild(stats.dom);

        // 创建3D体积纹理
        createVolumeTexture();

        // 创建星云
        createNebula();

        // 创建星尘粒子系统
        createStardust();

        // 创建GUI
        createGUI();

        // 窗口大小调整
        window.addEventListener("resize", onWindowResize);

        // 开始动画循环
        animate();
      }

      // 创建3D体积纹理
      function createVolumeTexture() {
        const size = 128;
        const data = new Uint8Array(size * size * size);
        const noise = new ImprovedNoise();

        let i = 0;
        for (let z = 0; z < size; z++) {
          for (let y = 0; y < size; y++) {
            for (let x = 0; x < size; x++) {
              // 将坐标标准化到-1到1范围
              const nx = (x / size) * 2 - 1;
              const ny = (y / size) * 2 - 1;
              const nz = (z / size) * 2 - 1;

              // 距离中心的距离
              const distanceFromCenter = Math.sqrt(nx * nx + ny * ny + nz * nz);

              // 创建球形衰减
              let sphericalFalloff = Math.max(0, 1 - distanceFromCenter / 0.8);
              sphericalFalloff = Math.pow(sphericalFalloff, 2);

              // 多层噪声
              let noiseValue = 0;
              let amplitude = 1;
              let frequency = 1;

              // 基础噪声层
              for (let octave = 0; octave < 6; octave++) {
                noiseValue +=
                  amplitude *
                  noise.noise(nx * frequency, ny * frequency, nz * frequency);
                amplitude *= 0.5;
                frequency *= 2;
              }

              // 湍流噪声
              let turbulence = 0;
              amplitude = 0.5;
              frequency = 2;
              for (let octave = 0; octave < 4; octave++) {
                turbulence +=
                  amplitude *
                  Math.abs(
                    noise.noise(
                      nx * frequency + 100,
                      ny * frequency + 100,
                      nz * frequency + 100
                    )
                  );
                amplitude *= 0.5;
                frequency *= 2;
              }

              // 脊状噪声
              let ridged = 0;
              amplitude = 0.3;
              frequency = 1.5;
              for (let octave = 0; octave < 3; octave++) {
                let n = noise.noise(
                  nx * frequency + 200,
                  ny * frequency + 200,
                  nz * frequency + 200
                );
                n = 1 - Math.abs(n);
                n = n * n;
                ridged += amplitude * n;
                amplitude *= 0.5;
                frequency *= 2;
              }

              // 组合所有噪声
              let finalNoise =
                noiseValue * 0.5 + turbulence * 0.3 + ridged * 0.2;
              finalNoise = (finalNoise + 1) * 0.5; // 标准化到0-1

              // 应用球形衰减
              finalNoise *= sphericalFalloff;

              // 增强对比度
              finalNoise = Math.pow(finalNoise, 1.5);

              // 转换为0-255范围
              data[i] = Math.floor(
                Math.max(0, Math.min(255, finalNoise * 255))
              );
              i++;
            }
          }
        }

        volumeTexture = new THREE.Data3DTexture(data, size, size, size);
        volumeTexture.format = THREE.RedFormat;
        volumeTexture.type = THREE.UnsignedByteType;
        volumeTexture.minFilter = THREE.LinearFilter;
        volumeTexture.magFilter = THREE.LinearFilter;
        volumeTexture.wrapS = THREE.ClampToEdgeWrapping;
        volumeTexture.wrapT = THREE.ClampToEdgeWrapping;
        volumeTexture.wrapR = THREE.ClampToEdgeWrapping;
        volumeTexture.needsUpdate = true;
      }

      // 创建星云
      function createNebula() {
        const geometry = new THREE.BoxGeometry(2, 2, 2);

        // 获取着色器代码
        const vertexShader = document.getElementById("vertex").textContent;
        const fragmentShader = document.getElementById("fragment").textContent;

        const material = new THREE.ShaderMaterial({
          vertexShader: vertexShader,
          fragmentShader: fragmentShader,
          uniforms: {
            time: { value: 0 },
            cameraPosition: { value: camera.position },
            volumeTexture: { value: volumeTexture },
            nebulaColor1: { value: new THREE.Color(params.nebulaColor1) },
            nebulaColor2: { value: new THREE.Color(params.nebulaColor2) },
            nebulaColor3: { value: new THREE.Color(params.nebulaColor3) },
            nebulaColor4: { value: new THREE.Color(params.nebulaColor4) },
            density: { value: params.density },
            brightness: { value: params.brightness },
            turbulence: { value: params.turbulence },
            absorption: { value: params.absorption },
          },
          transparent: true,
          side: THREE.BackSide,
          depthWrite: false,
          blending: THREE.AdditiveBlending,
        });

        nebulaMesh = new THREE.Mesh(geometry, material);
        scene.add(nebulaMesh);
      }

      // 创建星尘粒子系统
      function createStardust() {
        const geometry = new THREE.BufferGeometry();
        const positions = [];
        const colors = [];
        const sizes = [];
        const brightness = [];
        const twinkle = [];

        // 星尘颜色调色板
        const starColors = [
          new THREE.Color(0xffffff), // 白色
          new THREE.Color(0xadd8e6), // 淡蓝色
          new THREE.Color(0xffd700), // 金色
          new THREE.Color(0xffa500), // 橙色
          new THREE.Color(0xff69b4), // 粉色
          new THREE.Color(0x9370db), // 紫色
        ];

        for (let i = 0; i < params.stardustCount; i++) {
          // 在球形区域内随机分布
          const radius = Math.random() * 1.5;
          const theta = Math.random() * Math.PI * 2;
          const phi = Math.acos(2 * Math.random() - 1);

          const x = radius * Math.sin(phi) * Math.cos(theta);
          const y = radius * Math.sin(phi) * Math.sin(theta);
          const z = radius * Math.cos(phi);

          positions.push(x, y, z);

          // 随机选择颜色
          const color =
            starColors[Math.floor(Math.random() * starColors.length)];
          colors.push(color.r, color.g, color.b);

          // 随机大小
          sizes.push(Math.random() * params.stardustSize + 0.5);

          // 随机亮度
          brightness.push(Math.random() * 0.8 + 0.2);

          // 随机闪烁频率
          twinkle.push(Math.random() * 3 + 1);
        }

        geometry.setAttribute(
          "position",
          new THREE.Float32BufferAttribute(positions, 3)
        );
        geometry.setAttribute(
          "color",
          new THREE.Float32BufferAttribute(colors, 3)
        );
        geometry.setAttribute(
          "size",
          new THREE.Float32BufferAttribute(sizes, 1)
        );
        geometry.setAttribute(
          "brightness",
          new THREE.Float32BufferAttribute(brightness, 1)
        );
        geometry.setAttribute(
          "twinkle",
          new THREE.Float32BufferAttribute(twinkle, 1)
        );

        // 获取星尘着色器
        const vertexShader =
          document.getElementById("stardustVertex").textContent;
        const fragmentShader =
          document.getElementById("stardustFragment").textContent;

        const material = new THREE.ShaderMaterial({
          vertexShader: vertexShader,
          fragmentShader: fragmentShader,
          uniforms: {
            time: { value: 0 },
          },
          transparent: true,
          vertexColors: true,
          blending: THREE.AdditiveBlending,
          depthWrite: false,
        });

        stardustSystem = new THREE.Points(geometry, material);
        scene.add(stardustSystem);
      }

      // 创建GUI控制面板
      function createGUI() {
        const gui = new GUI();

        // 星云颜色控制
        const nebulaFolder = gui.addFolder("星云颜色");
        nebulaFolder
          .addColor(params, "nebulaColor1")
          .name("颜色1")
          .onChange(updateNebulaColors);
        nebulaFolder
          .addColor(params, "nebulaColor2")
          .name("颜色2")
          .onChange(updateNebulaColors);
        nebulaFolder
          .addColor(params, "nebulaColor3")
          .name("颜色3")
          .onChange(updateNebulaColors);
        nebulaFolder
          .addColor(params, "nebulaColor4")
          .name("颜色4")
          .onChange(updateNebulaColors);

        // 星云效果控制
        const effectsFolder = gui.addFolder("星云效果");
        effectsFolder
          .add(params, "density", 0, 2)
          .name("密度")
          .onChange(updateNebulaUniforms);
        effectsFolder
          .add(params, "brightness", 0, 3)
          .name("亮度")
          .onChange(updateNebulaUniforms);
        effectsFolder
          .add(params, "turbulence", 0, 1)
          .name("湍流")
          .onChange(updateNebulaUniforms);
        effectsFolder
          .add(params, "absorption", 0, 1)
          .name("吸收")
          .onChange(updateNebulaUniforms);

        // 动画控制
        const animationFolder = gui.addFolder("动画");
        animationFolder.add(params, "animationSpeed", 0, 3).name("动画速度");

        // 星尘控制
        const stardustFolder = gui.addFolder("星尘");
        stardustFolder
          .add(params, "stardustSize", 0.5, 5)
          .name("粒子大小")
          .onChange(updateStardustSize);

        nebulaFolder.open();
        effectsFolder.open();
      }

      // 更新星云颜色
      function updateNebulaColors() {
        if (nebulaMesh) {
          nebulaMesh.material.uniforms.nebulaColor1.value.set(
            params.nebulaColor1
          );
          nebulaMesh.material.uniforms.nebulaColor2.value.set(
            params.nebulaColor2
          );
          nebulaMesh.material.uniforms.nebulaColor3.value.set(
            params.nebulaColor3
          );
          nebulaMesh.material.uniforms.nebulaColor4.value.set(
            params.nebulaColor4
          );
        }
      }

      // 更新星云uniform变量
      function updateNebulaUniforms() {
        if (nebulaMesh) {
          nebulaMesh.material.uniforms.density.value = params.density;
          nebulaMesh.material.uniforms.brightness.value = params.brightness;
          nebulaMesh.material.uniforms.turbulence.value = params.turbulence;
          nebulaMesh.material.uniforms.absorption.value = params.absorption;
        }
      }

      // 更新星尘大小
      function updateStardustSize() {
        if (stardustSystem) {
          const sizes = stardustSystem.geometry.attributes.size.array;
          for (let i = 0; i < sizes.length; i++) {
            sizes[i] = Math.random() * params.stardustSize + 0.5;
          }
          stardustSystem.geometry.attributes.size.needsUpdate = true;
        }
      }

      // 窗口大小调整
      function onWindowResize() {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
      }

      // 动画循环
      function animate() {
        requestAnimationFrame(animate);

        const elapsedTime = clock.getElapsedTime() * params.animationSpeed;

        // 更新星云动画
        if (nebulaMesh) {
          nebulaMesh.material.uniforms.time.value = elapsedTime;
          nebulaMesh.material.uniforms.cameraPosition.value.copy(
            camera.position
          );

          // 缓慢旋转星云
          nebulaMesh.rotation.y += 0.001;
          nebulaMesh.rotation.x += 0.0005;
        }

        // 更新星尘动画
        if (stardustSystem) {
          stardustSystem.material.uniforms.time.value = elapsedTime;

          // 缓慢旋转星尘
          stardustSystem.rotation.y += 0.0008;
          stardustSystem.rotation.z += 0.0003;
        }

        // 更新控制器
        controls.update();

        // 渲染场景
        renderer.render(scene, camera);

        // 更新性能监控
        stats.update();
      }

      // 启动应用
      init();
    </script>
  </body>
</html>
