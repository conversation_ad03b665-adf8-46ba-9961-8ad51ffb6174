uniform float uA;
uniform float uB;
varying vec2 vUv;
#define PI 3.1415926535897932384626433832795
#define E 2.71828182845904523536
float func(float x){
  return -pow(E,(-(x*x)/2.0)) / sqrt(2.0*PI);
}

void main(){
    vec2 center = vec2(0.0,0.0);
    vUv = uv;
    vec4 meshP =  vec4(position, 1.0); 
    float dis = distance(position.xy,vec2(uv.x - 0.5, uv.y - 0.5))*uA;
    meshP.z += sin(dis)*uB;
    
    // meshP.z = sin(position.x);
    // meshP.z += sin(position.y);
    vec4 modeV = modelViewMatrix * meshP;
    
    vec4 pm = projectionMatrix * modeV;
    gl_Position = pm;
}

