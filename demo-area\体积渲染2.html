<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Volumetric Fog in Three.js</title>
    <style>
      body {
        margin: 0;
        overflow: hidden;
      }
      canvas {
        display: block;
      }
    </style>
  </head>
  <body>
    <script type="importmap">
      {
        "imports": {
          "three": "../lib/three.module.js",
          "three/examples/jsm/controls/OrbitControls": "../lib/examples/jsm/controls/OrbitControls.js",
          "three/examples/jsm/loaders/TextureLoader": "../lib/examples/jsm/loaders/TextureLoader.js",
          "three/addons/libs/lil-gui.module.min.js": "../lib/examples/jsm/libs/lil-gui.module.min.js",
          "three/addons/libs/stats.module.js": "../lib/examples/jsm/libs/stats.module.js",
          "three/addons/math/ImprovedNoise.js": "../lib/examples/jsm/math/ImprovedNoise.js"
        }
      }
    </script>
    <script type="text" id="vertex">
        varying vec3 vWorldPos;
      void main() {
        vWorldPos = (modelMatrix * vec4(position, 1.0)).xyz;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    </script>
    <script type="text" id="fragment">
        precision highp float;
      uniform sampler3D volume;
      uniform vec3 cameraPos;
      varying vec3 vWorldPos;

      float sampleVolume(vec3 p) {
        return texture(volume, p).r;
      }

      void main() {
        vec3 rayDir = normalize(vWorldPos - cameraPos);
        vec3 rayOrigin = vWorldPos;

        // Transform ray to [0,1] box
        rayOrigin = (rayOrigin + vec3(0.5)) / 1.0;

        float t = 0.0;
        float maxT = 2.0;
        float stepSize = 0.01;

        vec3 col = vec3(0.0);
        float alpha = 0.0;

        for (int i = 0; i < 256; i++) {
          vec3 pos = rayOrigin + rayDir * t;
          if (any(lessThan(pos, vec3(0.0))) || any(greaterThan(pos, vec3(1.0)))) break;

          float d = sampleVolume(pos);
          float a = d * 0.1;

          vec3 c = vec3(d * 0.6, d * 0.8, d); // 紫蓝星云

          c *= a;
          col += (1.0 - alpha) * c;
          alpha += (1.0 - alpha) * a;

          if (alpha > 0.95) break;
          t += stepSize;
          if (t > maxT) break;
        }

        gl_FragColor = vec4(col, alpha);
      }
    </script>
    <script type="module">
      import * as THREE from "three";
      import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
      import { ImprovedNoise } from "three/addons/math/ImprovedNoise.js";

      const vertexShader = document.getElementById("vertex").textContent;
      const fragmentShader = document.getElementById("fragment").textContent;
      console.log(vertexShader);

      // Create scene
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(
        45,
        window.innerWidth / window.innerHeight,
        0.1,
        100
      );
      camera.position.set(0, 0, 2);

      // Renderer
      const renderer = new THREE.WebGLRenderer();
      renderer.setSize(window.innerWidth, window.innerHeight);
      document.body.appendChild(renderer.domElement);

      // Controls
      const controls = new OrbitControls(camera, renderer.domElement);

      const size = 128;
      const center = size / 2;
      const data = new Uint8Array(size * size * size);

      for (let z = 0; z < size; z++) {
        const nz = (z - center) / center;

        for (let y = 0; y < size; y++) {
          const ny = (y - center) / center;

          for (let x = 0; x < size; x++) {
            const nx = (x - center) / center;

            const dx = nx;
            const dy = ny;
            const dz = nz * 0.3; // 饼状压扁

            const r = Math.sqrt(dx * dx + dy * dy);
            const theta = Math.atan2(dy, dx);

            // 生成螺旋：亮度按角度 + 半径偏移形成多臂
            const arms = 3;
            const spiral = Math.sin(theta * arms + r * 10.0);

            // 星云中心亮度随距离衰减
            let density = Math.exp(-r * 5.0) * Math.exp(-dz * dz * 10.0);
            density *= 0.5 + 0.5 * spiral;

            // 模拟噪声扰动边缘
            const noise = (Math.random() - 0.5) * 0.1;
            density += noise;

            density = Math.min(Math.max(density, 0), 1);

            const index = x + y * size + z * size * size;
            data[index] = density * 255;
          }
        }
      }

      const texture = new THREE.Data3DTexture(data, size, size, size);
      texture.format = THREE.RedFormat;
      texture.type = THREE.UnsignedByteType;
      texture.minFilter = THREE.LinearFilter;
      texture.magFilter = THREE.LinearFilter;
      texture.unpackAlignment = 1;
      texture.needsUpdate = true;
      // Shader Material
      const material = new THREE.ShaderMaterial({
        vertexShader,
        fragmentShader,
        uniforms: {
          uTexture: { value: texture },
          cameraPos: { value: camera.position },
          uResolution: {
            value: new THREE.Vector2(window.innerWidth, window.innerHeight),
          },
        },
        side: THREE.BackSide,
      });

      const geometry = new THREE.BoxGeometry(1, 1, 0.1);
      const mesh = new THREE.Mesh(geometry, material);
      scene.add(mesh);

      // Resize
      window.addEventListener("resize", () => {
        renderer.setSize(window.innerWidth, window.innerHeight);
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
      });

      // Animate
      renderer.setAnimationLoop(() => {
        controls.update();
        material.uniforms.cameraPos.value.copy(camera.position);
        renderer.render(scene, camera);
      });
    </script>
  </body>
</html>
