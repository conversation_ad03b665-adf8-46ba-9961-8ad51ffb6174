import * as THREE from "three";
export default async function no1(THREE) {
  const { vertexShader, fragmentShader } = await getGlsl();
  // 创建一个球体几何体
  const geometry = new THREE.PlaneGeometry(100, 100, 10, 10);
  const material = new THREE.ShaderMaterial({
    vertexShader,
    fragmentShader,
    transparent: true,
    // wireframe: true,
    side: THREE.DoubleSide,
    depthWrite: false,
    // wireframe: true,
    uniforms: {
      iTime: {
        value: 1.0,
      },
      iResolution: {
        value: new THREE.Vector2(window.innerWidth, window.innerHeight),
      },
      uA: {
        value: 30,
      },
      uB: {
        value: 0.01,
      },
      uR: {
        value: 0.015,
      },
      uH: {
        value: 0.0,
      },

      // ulength: {
      //   value: 1.0,
      // },
      // iResolution: {
      //   value: new THREE.Vector2(window.innerWidth, window.innerHeight),
      // },
    },
    // 混合模式
    blending: THREE.AdditiveBlending,
  });
  // 初始化
  Object.assign(material.uniforms, getUniformsConfig());
  const mesh = new THREE.Mesh(geometry, material);
  mesh.rotateX(-Math.PI / 2);
  mesh.position.set(0, 0, 0);
  return mesh;
}

async function getGlsl(vetFileName = "vert.glsl", fragFilename = "frag.glsl") {
  const base = "./no1/";
  function _readFile(path) {
    return new Promise(async (resolve, reject) => {
      const res = await fetch(base + path);
      const code = await res.text();
      resolve(code);
    });
  }

  const vertexShader = await _readFile(vetFileName);
  const fragmentShader = await _readFile(fragFilename);
  return { vertexShader, fragmentShader };
}

function getUniformsConfig() {
  const store = localStorage.getItem("uniforms-config");
  return store ? JSON.parse(store) : {};
}

export function setUniformsConfig(config) {
  localStorage.setItem("uniforms-config", JSON.stringify(config));
}
0;
//粒子系统
const params = {
  particleCount: 105000,
  particleSize: 0.02,
  branches: 6,
  branchRadius: 3.0,
  spin: 1.0,
  radialRandomness: 1.0,
  innerColor: "#f8b152",
  outerColor: "#6971a4",
};

export async function getParticleSystem(THREE) {
  const particleTexture = new THREE.TextureLoader().load(
    "./images/galactic_blur.png"
  );
  const { vertexShader, fragmentShader } = await getGlsl(
    "centerVert.glsl",
    "centerFrag.glsl"
  );

  const material = new THREE.ShaderMaterial({
    vertexShader,
    fragmentShader,
    transparent: true,
    depthWrite: false,
    uniforms: {
      uTexture: {
        value: particleTexture,
      },
      uAlphaMap: {
        value: particleTexture,
      },
      uCenterColor: {
        value: new THREE.Color("#ffffff"),
      },
    },
    blending: THREE.AdditiveBlending, //加法混合
  });

  const innerColor = new THREE.Color(params.innerColor);
  const outerColor = new THREE.Color(params.outerColor);

  const geometry = new THREE.BufferGeometry();
  const position = [];
  const colors = [];
  const size = [];

  // 定义核心节
  for (let i = 0; i < 5; i++) {
    const centerPosition = new THREE.Vector3(0, 0, 0);
    const color = new THREE.Color("#f8b152");
    const centerColor = [color.r, color.g, color.b];
    position.push(centerPosition.x, centerPosition.y, centerPosition.z);
    colors.push(...centerColor);
    size.push(50);
  }

  const hazes = [];
  for (let i = 0; i < params.particleCount; i++) {
    let i3 = i * 3; //xyz一次循环
    const radius = params.branchRadius * Math.random();
    const branchAngle = ((i % params.branches) / params.branches) * Math.PI * 2;
    const spinAngle = params.spin * radius * Math.PI * 2;
    const randRadius = Math.random() * params.radialRandomness * radius;
    let { x, y, z } = generatePosition(randRadius);
    x += radius * Math.cos(branchAngle + spinAngle);
    z += radius * Math.sin(branchAngle + spinAngle);
    position.push(x, y, z);
    // 定义颜色
    const color = innerColor.clone();
    color.lerp(outerColor, radius / params.branchRadius);
    colors.push(color.r, color.g, color.b);
    if (i % 12 === 0)
      hazes.push(createHaze(new THREE.Vector3(x, y, z), color.getHex()));

    // const rsize = randomSize(0.6, 2.2);
    size.push(i % 1000 === 0 ? (Math.random() > 0.5 ? 8 : 0.7) : 0.7); //默认尺寸为1
  }

  geometry.setAttribute(
    "position",
    new THREE.Float32BufferAttribute(position, 3)
  );
  geometry.setAttribute("color", new THREE.Float32BufferAttribute(colors, 3));
  geometry.setAttribute("size", new THREE.Float32BufferAttribute(size, 1));
  const points = new THREE.Points(geometry, material);
  points.rotateX(Math.PI);
  // console.log(hazes);
  return [points, ...hazes];
}

function generatePosition(radius) {
  // const y = 0; //y轴
  const theta = Math.random() * Math.PI * 2;
  const phi = Math.random() * Math.PI * 2;
  const x = radius * Math.sin(theta) * Math.cos(phi);
  const y = radius * Math.sin(theta) * Math.sin(phi) * 0.3;
  const z = radius * Math.cos(theta);
  return { x, y, z };
}

export function createCloud(THREE) {
  const textTrue = new THREE.TextureLoader().load("./images/galactictop.png");
  const planeGeometry = new THREE.PlaneGeometry(15, 15, 100, 100);
  const planeMaterial = new THREE.MeshBasicMaterial({
    color: 0xffffff,
    side: THREE.DoubleSide,
    map: textTrue,
    alphaMap: textTrue,
    transparent: true,
    opacity: 1.0,
    depthWrite: false,
    blending: THREE.AdditiveBlending,
  });
  const mesh = new THREE.Mesh(planeGeometry, planeMaterial);
  mesh.rotateX(Math.PI / 2);
  return mesh;
}

export async function area(THREE) {
  const group = new THREE.Group();
  const particleSystem = await getParticleSystem(THREE);
  // particleSystem.merge;
  group.add(...particleSystem);
  group.add(createCloud(THREE));
  group.add(createCloud(THREE));
  function animate() {
    // group.rotateY(0.0005);
    requestAnimationFrame(animate);
    // particleSystem.rotateY(-0.0001);
  }
  animate();
  return group;
}

const hazeTexture = new THREE.TextureLoader().load("./images/feathered60.png");
export function createHaze(position, color = 0x0082ff) {
  const hazeSprite = new THREE.SpriteMaterial({
    map: hazeTexture,
    color,
    transparent: true,
    opacity: 0.1,
    depthTest: false,
    depthWrite: false,
  });

  const sprite = new THREE.Sprite(hazeSprite);

  sprite.position.copy(position);
  sprite.position.y = sprite.position.y * 0.5;
  // sprite.scale.multiplyScalar(clamp(50.0 * Math.random(), 20.0, 50.0));
  sprite.scale.set(0.5, 0.5, 0.5);
  return sprite;
}

export function clamp(value, minimum, maximum) {
  return Math.min(maximum, Math.max(minimum, value));
}
