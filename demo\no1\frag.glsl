uniform float uA;
uniform float uB;
uniform float uR;
uniform float uH;
#define PI 3.1415926535897932384626433832795
vec2 rotate(vec2 uv, float rotation, vec2 mid) {
  return vec2(
    cos(rotation) * (uv.x - mid.x) + sin(rotation) * (uv.y - mid.y) + mid.x,
    cos(rotation) * (uv.y - mid.y) - sin(rotation) * (uv.x - mid.x) + mid.y
  );
}
void main() {
        vec2 rotatedUv = rotate(gl_PointCoord, PI * 0.5, vec2(0.5));
        float strength = uA / (distance(vec2(rotatedUv.x, (rotatedUv.y - 0.5) * 5.0 + 0.5), vec2(0.5)));
        strength *= uA / (distance(vec2(rotatedUv.y, (rotatedUv.x - 0.5) * 5.0 + 0.5), vec2(0.5)));
        strength = 0.015/distance(gl_PointCoord, vec2(0.5));
        // if(strength <= 0.09)discard;
        gl_FragColor = vec4(1.0,0.0,0.0, strength);
}
