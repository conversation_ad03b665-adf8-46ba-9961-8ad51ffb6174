import * as THREE from "three";
import { Star } from "./star.js";
import {
  ARMS,
  ARM_X_DIST,
  ARM_X_MEAN,
  ARM_Y_DIST,
  ARM_Y_MEAN,
  CORE_X_DIST,
  CORE_Y_DIST,
  GALAXY_THICKNESS,
  HAZE_RATIO,
  NUM_STARS,
  OUTER_CORE_X_DIST,
  OUTER_CORE_Y_DIST,
} from "../config/galaxyConfig.js";
import { gaussianRandom, spiral } from "../utils.js";
import { Haze } from "./haze.js";

//粒子系统
const params = {
  particleCount: 15000,
  particleSize: 0.02,
  branches: 3,
  branchRadius: 150.0,
  spin: 5.0,
  radialRandomness: 1.0,
  innerColor: "#f8b152",
  outerColor: "#6971a4",
};

export class Galaxy {
  constructor(scene) {
    this.scene = scene;

    this.stars = this.generateObject(NUM_STARS, (pos) => new Star(pos));
    this.haze = this.generateObject(NUM_STARS, (pos) => new Haze(pos));

    this.stars.forEach((star) => star.toThreeObject(scene));
    this.haze.forEach((haze) => haze.toThreeObject(scene));
  }

  updateScale(camera) {
    this.stars.forEach((star) => {
      star.updateScale(camera);
    });

    this.haze.forEach((haze) => {
      haze.updateScale(camera);
    });
  }

  generateObject(numStars, generator) {
    let objects = [];
    for (let i = 0; i < numStars; i++) {
      const { x, y, z } = this.createPos(i);
      objects.push(generator(new THREE.Vector3(x, y, z)));
    }
    // for (let i = 0; i < numStars / 4; i++) {
    //   let pos = new THREE.Vector3(
    //     gaussianRandom(0, CORE_X_DIST),
    //     gaussianRandom(0, CORE_Y_DIST),
    //     gaussianRandom(0, GALAXY_THICKNESS)
    //   );
    //   let obj = generator(pos);
    //   objects.push(obj);
    // }

    // for (let i = 0; i < numStars / 4; i++) {
    //   let pos = new THREE.Vector3(
    //     gaussianRandom(0, OUTER_CORE_X_DIST),
    //     gaussianRandom(0, OUTER_CORE_Y_DIST),
    //     gaussianRandom(0, GALAXY_THICKNESS)
    //   );
    //   let obj = generator(pos);
    //   objects.push(obj);
    // }

    // for (let j = 0; j < ARMS; j++) {
    //   for (let i = 0; i < numStars / 4; i++) {
    //     let pos = spiral(
    //       gaussianRandom(ARM_X_MEAN, ARM_X_DIST),
    //       gaussianRandom(ARM_Y_MEAN, ARM_Y_DIST),
    //       gaussianRandom(0, GALAXY_THICKNESS),
    //       (j * 2 * Math.PI) / ARMS
    //     );
    //     let obj = generator(pos);
    //     objects.push(obj);
    //   }
    // }
    // console.log()
    return objects;
  }

  //坐标生成
  createPos(i) {
    const radius = params.branchRadius * Math.random();
    const branchAngle = ((i % params.branches) / params.branches) * Math.PI * 2;
    const spinAngle = params.spin * radius * Math.PI * 2;
    const randRadius = Math.random() * params.radialRandomness * radius;
    let { x, y, z } = this.generatePosition(randRadius);
    x += radius * Math.cos(branchAngle + spinAngle);
    z += radius * Math.sin(branchAngle + spinAngle);
    return {
      x,
      y,
      z,
    };
  }
  generatePosition(radius) {
    // const y = 0; //y轴
    const theta = Math.random() * Math.PI * 2;
    const phi = Math.random() * Math.PI * 2;
    const x = radius * Math.sin(theta) * Math.cos(phi);
    const y = radius * Math.sin(theta) * Math.sin(phi) * 0.2;
    const z = radius * Math.cos(theta);
    return { x, y, z };
  }
}
