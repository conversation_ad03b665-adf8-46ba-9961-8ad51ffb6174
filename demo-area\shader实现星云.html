<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Volumetric Disk Cloud with Data3DTexture</title>
    <style>
      body {
        margin: 0;
        overflow: hidden;
        background: #000;
      }
      canvas {
        display: block;
      }
    </style>
  </head>
  <body>
    <script type="importmap">
      {
        "imports": {
          "three": "../lib/three.module.js",
          "three/examples/jsm/controls/OrbitControls": "../lib/examples/jsm/controls/OrbitControls.js",
          "three/examples/jsm/loaders/TextureLoader": "../lib/examples/jsm/loaders/TextureLoader.js",
          "three/addons/libs/lil-gui.module.min.js": "../lib/examples/jsm/libs/lil-gui.module.min.js",
          "three/addons/libs/stats.module.js": "../lib/examples/jsm/libs/stats.module.js",
          "three/addons/math/ImprovedNoise.js": "../lib/examples/jsm/math/ImprovedNoise.js"
        }
      }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.158.0/build/three.min.js"></script>
    <script type="module">
      import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";

      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(
        75,
        window.innerWidth / window.innerHeight,
        0.1,
        100
      );
      camera.position.z = 4;

      const renderer = new THREE.WebGLRenderer();
      renderer.setSize(window.innerWidth, window.innerHeight);
      document.body.appendChild(renderer.domElement);

      const controls = new OrbitControls(camera, renderer.domElement);

      const SIZE = 128;
      const data = new Uint8Array(SIZE * SIZE * SIZE);

      for (let z = 0; z < SIZE; z++) {
        for (let y = 0; y < SIZE; y++) {
          for (let x = 0; x < SIZE; x++) {
            const dx = (x - SIZE / 2) / (SIZE / 2);
            const dy = (y - SIZE / 2) / (SIZE / 2);
            const dz = (z - SIZE / 2) / (SIZE / 2);

            // Disk-shaped density falloff
            const r = Math.sqrt(dx * dx + dz * dz);
            const h = dy;
            const value =
              r < 1.0 && Math.abs(h) < 0.2
                ? (1.0 - r * r) * (1.0 - h * h * 25.0)
                : 0.0;
            const density = Math.max(0, Math.min(255, value * 255));

            data[x + y * SIZE + z * SIZE * SIZE] = density;
          }
        }
      }

      const texture = new THREE.Data3DTexture(data, SIZE, SIZE, SIZE);
      texture.format = THREE.RedFormat;
      texture.type = THREE.UnsignedByteType;
      texture.minFilter = THREE.LinearFilter;
      texture.magFilter = THREE.LinearFilter;
      texture.unpackAlignment = 1;
      texture.needsUpdate = true;

      const vertexShader = `
        varying vec3 vWorldPosition;
        void main() {
          vec4 worldPosition = modelMatrix * vec4(position, 1.0);
          vWorldPosition = worldPosition.xyz;
          gl_Position = projectionMatrix * viewMatrix * worldPosition;
        }
      `;

      const fragmentShader = `
        precision highp float;
  precision highp sampler3D;

  uniform sampler3D volume;
  uniform vec3 cameraPosition2;
  varying vec3 vWorldPosition;

  void main() {
    vec3 rayDir = normalize(vWorldPosition - cameraPosition2);
    vec3 rayOrigin = cameraPosition2;
    float t = 0.0;
    float maxDistance = 3.0;
    float stepSize = 0.02;
    vec3 color = vec3(0.0);

    for (int i = 0; i < 150; i++) {
      vec3 p = rayOrigin + rayDir * t;
      vec3 lp = (p + 1.0) / 2.0; // map to [0,1]
      float density = texture(volume, lp).r;
      color += density * vec3(0.6, 0.6, 0.65);
      if (t > maxDistance) break;
      t += stepSize;
    }

    gl_FragColor = vec4(color, 1.0);
  }
      `;

      const material = new THREE.ShaderMaterial({
        vertexShader,
        fragmentShader,
        transparent: true,
        depthWrite: false,
        uniforms: {
          volume: { value: texture },
          cameraPosition: { value: camera.position },
        },
      });

      const geometry = new THREE.BoxGeometry(2, 0.5, 2);
      const mesh = new THREE.Mesh(geometry, material);
      scene.add(mesh);

      function animate() {
        requestAnimationFrame(animate);
        mesh.rotation.y += 0.002;
        renderer.render(scene, camera);
      }

      animate();

      window.addEventListener("resize", () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
      });
    </script>
  </body>
</html>
