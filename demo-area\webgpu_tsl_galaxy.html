<!DOCTYPE html>
<html lang="en">
  <head>
    <title>three.js webgpu - galaxy</title>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0"
    />
    <!-- <link type="text/css" rel="stylesheet" href="main.css" /> -->
  </head>
  <body>
    <div id="info">
      <a href="https://threejs.org" target="_blank" rel="noopener"
        >three.js webgpu</a
      >
      - galaxy
      <br />
      Based on
      <a
        href="https://threejs-journey.com/lessons/animated-galaxy"
        target="_blank"
        rel="noopener"
        >Three.js Journey</a
      >
      lessons
    </div>

    <!-- <script type="importmap">
			{
				"imports": {
					"three": "../build/three.webgpu.js",
					"three/webgpu": "../build/three.webgpu.js",
					"three/tsl": "../build/three.tsl.js",
					"three/addons/": "./jsm/"
				}
			}
		</script> -->
    <script type="importmap">
      {
        "imports": {
          "three": "../lib/three.webgpu.js",
          "three/webgpu": "../lib/three.webgpu.js",
          "three/examples/jsm/controls/OrbitControls": "../lib/examples/jsm/controls/OrbitControls.js",
          "three/examples/jsm/loaders/TextureLoader": "../lib/examples/jsm/loaders/TextureLoader.js",
          "three/addons/libs/lil-gui.module.min.js": "../lib/examples/jsm/libs/lil-gui.module.min.js",
          "three/addons/libs/stats.module.js": "../lib/examples/jsm/libs/stats.module.js",
          "three/addons/math/ImprovedNoise.js": "../lib/examples/jsm/math/ImprovedNoise.js",
          "three/tsl": "../lib/three.tsl.js"
        }
      }
    </script>

    <script type="module">
      import * as THREE from "three";
      import {
        color,
        cos,
        float,
        mix,
        range,
        sin,
        time,
        uniform,
        uv,
        vec3,
        vec4,
        PI2,
      } from "three/tsl";

      import { GUI } from "three/addons/libs/lil-gui.module.min.js";
      import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";

      let camera, scene, renderer, controls;

      init();

      function init() {
        camera = new THREE.PerspectiveCamera(
          50,
          window.innerWidth / window.innerHeight,
          0.1,
          100
        );
        camera.position.set(4, 2, 5);

        scene = new THREE.Scene();
        scene.background = new THREE.Color(0x201919);

        // galaxy

        const material = new THREE.SpriteNodeMaterial({
          depthWrite: false,
          blending: THREE.AdditiveBlending,
        });

        const size = uniform(0.018);
        console.log("size", size);
        material.scaleNode = range(0, 1).mul(size);

        const radiusRatio = range(0, 0.3);
        const radius = radiusRatio.pow(1.5).mul(4).toVar();

        const branches = 6;
        // const branchAngle = range(0, branches).floor().mul(PI2.div(branches));
        // const angle = ;
        const branchAngle = range(0, branches).floor().mul(PI2.div(branches));
        const angleOffset = radiusRatio.pow(1).mul(20); //初始旋转角度
        const angle = branchAngle.add(angleOffset).add(time.mul(0.01));

        const position = vec3(cos(angle), 0, sin(angle)).mul(radius);

        const randomOffset = range(vec3(-1, -0.5, -1), vec3(1, 0.5, 1))
          .pow(3)
          .mul(radiusRatio)
          .add(0.2);

        material.positionNode = position.add(randomOffset);

        const colorInside = uniform(color("#ffa575"));
        const colorOutside = uniform(color("#311599"));
        const colorFinal = mix(
          colorInside,
          colorOutside,
          radiusRatio.oneMinus().pow(2).oneMinus()
        );
        const alpha = float(0.1).div(uv().sub(0.5).length()).sub(0.2);
        material.colorNode = vec4(colorFinal, alpha);

        const mesh = new THREE.InstancedMesh(
          new THREE.PlaneGeometry(1, 1),
          material,
          25000
        );
        scene.add(mesh);

        // debug

        const gui = new GUI();

        gui.add(size, "value", 0, 1, 0.001).name("size");

        gui
          .addColor(
            { color: colorInside.value.getHex(THREE.SRGBColorSpace) },
            "color"
          )
          .name("colorInside")
          .onChange(function (value) {
            colorInside.value.set(value);
          });

        gui
          .addColor(
            { color: colorOutside.value.getHex(THREE.SRGBColorSpace) },
            "color"
          )
          .name("colorOutside")
          .onChange(function (value) {
            colorOutside.value.set(value);
          });

        // renderer

        renderer = new THREE.WebGPURenderer({ antialias: true });
        renderer.setPixelRatio(window.devicePixelRatio);
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setAnimationLoop(animate);
        document.body.appendChild(renderer.domElement);

        controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.minDistance = 0.1;
        controls.maxDistance = 50;

        window.addEventListener("resize", onWindowResize);
      }

      function onWindowResize() {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();

        renderer.setSize(window.innerWidth, window.innerHeight);
      }

      function animate() {
        controls.update();

        renderer.render(scene, camera);
      }
    </script>
  </body>
</html>
